<!-- Students List Section -->
<div class="page-section" x-show="activeNav === 'students'">
    <div class="students-container">
        <!-- Search Bar (Desktop) -->
        <div class="search-bar desktop-only">
            <div class="search-input-container">
                <span class="material-icons search-icon">search</span>
                <input type="text" class="search-input" placeholder="Search students..." x-model="searchQuery" @input="filterStudents()">
                <button class="search-clear" x-show="searchQuery" @click="searchQuery = ''; filterStudents()">
                    <span class="material-icons">clear</span>
                </button>
            </div>
            <div class="search-filters">
                <select class="filter-select" x-model="selectedGrade" @change="filterStudents()">
                    <option value="">All Grades</option>
                    <option value="grade-1">Grade 1</option>
                    <option value="grade-2">Grade 2</option>
                    <option value="grade-3">Grade 3</option>
                    <option value="grade-4">Grade 4</option>
                    <option value="grade-5">Grade 5</option>
                </select>
                <select class="filter-select" x-model="selectedStatus" @change="filterStudents()">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>

        <!-- Students Grid -->
        <div class="students-grid" id="students-grid">
            <template x-for="student in filteredData" :key="student.id">
                <div class="student-card" @click="showStudentDetails(student)">
                    <div class="student-photo">
                        <img :src="student.photo || '/static/images/default-avatar.png'" :alt="student.name" loading="lazy">
                        <div class="student-status" :class="student.status">
                            <span class="material-icons" x-text="student.status === 'active' ? 'check_circle' : 'cancel'"></span>
                        </div>
                    </div>
                    <div class="student-info">
                        <div class="student-name" x-text="student.name"></div>
                        <div class="student-name-ar" x-text="student.nameAr" x-show="student.nameAr"></div>
                        <div class="student-id" x-text="student.studentId || 'No ID'"></div>
                        <div class="student-grade" x-text="student.grade"></div>
                    </div>
                    <div class="student-payment">
                        <div class="payment-amount">
                            <span class="paid-amount" x-text="formatCurrency(student.paidAmount)"></span>
                            <span class="payment-separator">/</span>
                            <span class="total-amount" x-text="formatCurrency(student.totalAmount)"></span>
                        </div>
                        <div class="payment-status" :class="getPaymentStatusClass(student)">
                            <span x-text="getPaymentStatusText(student)"></span>
                        </div>
                    </div>
                    <div class="student-actions">
                        <button class="student-action" @click.stop="editStudent(student)" title="Edit">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="student-action" @click.stop="addPayment(student)" title="Add Payment">
                            <span class="material-icons">payment</span>
                        </button>
                        <button class="student-action" @click.stop="showStudentOptions(student)" title="More Options">
                            <span class="material-icons">more_vert</span>
                        </button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Empty State -->
        <div class="empty-state" x-show="filteredData.length === 0">
            <div class="empty-state-icon">
                <span class="material-icons">people_outline</span>
            </div>
            <div class="empty-state-title">No students found</div>
            <div class="empty-state-message" x-show="searchQuery || selectedGrade || selectedStatus">
                Try adjusting your search criteria or filters
            </div>
            <div class="empty-state-message" x-show="!searchQuery && !selectedGrade && !selectedStatus">
                Start by adding your first student
            </div>
            <button class="mdc-button mdc-button--raised" @click="addStudent()">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons">add</span>
                <span class="mdc-button__label">Add Student</span>
            </button>
        </div>

        <!-- Loading State -->
        <div class="loading-state" x-show="isLoading">
            <div class="loading-spinner">
                <div class="mdc-circular-progress mdc-circular-progress--medium mdc-circular-progress--indeterminate">
                    <div class="mdc-circular-progress__determinate-container">
                        <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32">
                            <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                            <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__indeterminate-container">
                        <div class="mdc-circular-progress__spinner-layer">
                            <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32">
                                    <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__gap-patch">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32">
                                    <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32">
                                    <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text">Loading students...</div>
        </div>
    </div>
</div>
