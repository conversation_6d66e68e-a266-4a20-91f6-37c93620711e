/* Base Styles and CSS Variables */

:root {
  /* Material Design Color Palette */
  --md-primary: #1976d2;
  --md-primary-variant: #1565c0;
  --md-secondary: #03dac6;
  --md-secondary-variant: #018786;
  --md-background: #ffffff;
  --md-surface: #ffffff;
  --md-error: #b00020;
  --md-on-primary: #ffffff;
  --md-on-secondary: #000000;
  --md-on-background: #000000;
  --md-on-surface: #000000;
  --md-on-error: #ffffff;

  /* Extended Color Palette */
  --md-success: #4caf50;
  --md-warning: #ff9800;
  --md-info: #2196f3;
  --md-light: #f5f5f5;
  --md-dark: #212121;

  /* Typography */
  --md-font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --md-font-size-h1: 2.125rem;
  --md-font-size-h2: 1.5rem;
  --md-font-size-h3: 1.25rem;
  --md-font-size-h4: 1rem;
  --md-font-size-body1: 1rem;
  --md-font-size-body2: 0.875rem;
  --md-font-size-caption: 0.75rem;

  /* Spacing */
  --md-spacing-xs: 0.25rem;
  --md-spacing-sm: 0.5rem;
  --md-spacing-md: 1rem;
  --md-spacing-lg: 1.5rem;
  --md-spacing-xl: 2rem;
  --md-spacing-xxl: 3rem;

  /* Elevation (Box Shadows) */
  --md-elevation-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --md-elevation-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --md-elevation-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --md-elevation-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  --md-elevation-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

  /* Border Radius */
  --md-border-radius-sm: 4px;
  --md-border-radius-md: 8px;
  --md-border-radius-lg: 12px;
  --md-border-radius-xl: 16px;
  --md-border-radius-full: 50%;

  /* Transitions */
  --md-transition-fast: 0.15s ease-out;
  --md-transition-normal: 0.25s ease-out;
  --md-transition-slow: 0.35s ease-out;

  /* Z-Index Scale */
  --md-z-dropdown: 1000;
  --md-z-sticky: 1020;
  --md-z-fixed: 1030;
  --md-z-modal-backdrop: 1040;
  --md-z-modal: 1050;
  --md-z-popover: 1060;
  --md-z-tooltip: 1070;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --md-primary: #90caf9;
  --md-primary-variant: #64b5f6;
  --md-secondary: #80cbc4;
  --md-secondary-variant: #4db6ac;
  --md-background: #121212;
  --md-surface: #1e1e1e;
  --md-error: #cf6679;
  --md-on-primary: #000000;
  --md-on-secondary: #000000;
  --md-on-background: #ffffff;
  --md-on-surface: #ffffff;
  --md-on-error: #000000;
}

/* Base Reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: var(--md-font-family);
  font-size: var(--md-font-size-body1);
  line-height: 1.5;
  color: var(--md-on-background);
  background-color: var(--md-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: var(--md-spacing-sm);
}

h1 { font-size: var(--md-font-size-h1); }
h2 { font-size: var(--md-font-size-h2); }
h3 { font-size: var(--md-font-size-h3); }
h4 { font-size: var(--md-font-size-h4); }

p {
  margin-bottom: var(--md-spacing-md);
}

a {
  color: var(--md-primary);
  text-decoration: none;
  transition: color var(--md-transition-fast);
}

a:hover {
  color: var(--md-primary-variant);
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--md-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--md-surface);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--md-border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Selection Styles */
::selection {
  background-color: var(--md-primary);
  color: var(--md-on-primary);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.hidden { display: none; }
.visible { display: block; }

/* Responsive Utilities */
.desktop-only {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: block;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn var(--md-transition-normal) ease-out;
}

.fade-out {
  animation: fadeOut var(--md-transition-normal) ease-out;
}

.slide-up {
  animation: slideUp var(--md-transition-normal) ease-out;
}

.slide-down {
  animation: slideDown var(--md-transition-normal) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
