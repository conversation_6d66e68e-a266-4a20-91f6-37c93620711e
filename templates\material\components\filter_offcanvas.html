<!-- Filter Offcanvas Component -->
<div class="filter-offcanvas-overlay" id="filter-offcanvas-overlay">
    <div class="filter-offcanvas" id="filter-offcanvas">
        <div class="filter-offcanvas-header">
            <h2 class="filter-offcanvas-title">Filters</h2>
            <button class="filter-offcanvas-close" id="filter-offcanvas-close">
                <span class="material-icons">close</span>
            </button>
        </div>
        <div class="filter-offcanvas-content">
            <!-- Status Filter -->
            <div class="filter-section">
                <div class="filter-section-title">Status</div>
                <div class="filter-options">
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="status" value="active" checked>
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Active Students</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="status" value="inactive">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Inactive Students</span>
                    </label>
                </div>
            </div>

            <!-- Grade Filter -->
            <div class="filter-section">
                <div class="filter-section-title">Grade Level</div>
                <div class="filter-options">
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="grade" value="grade-1">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Grade 1</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="grade" value="grade-2">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Grade 2</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="grade" value="grade-3">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Grade 3</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="grade" value="grade-4">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Grade 4</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="grade" value="grade-5">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Grade 5</span>
                    </label>
                </div>
            </div>

            <!-- Payment Status Filter -->
            <div class="filter-section">
                <div class="filter-section-title">Payment Status</div>
                <div class="filter-options">
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="payment" value="paid">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Fully Paid</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="payment" value="partial">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Partially Paid</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="payment" value="unpaid">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Unpaid</span>
                    </label>
                </div>
            </div>

            <!-- Gender Filter -->
            <div class="filter-section">
                <div class="filter-section-title">Gender</div>
                <div class="filter-options">
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="gender" value="male">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Male</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" name="gender" value="female">
                        <span class="filter-checkmark"></span>
                        <span class="filter-label">Female</span>
                    </label>
                </div>
            </div>

            <!-- Date Range Filter -->
            <div class="filter-section">
                <div class="filter-section-title">Registration Date</div>
                <div class="filter-date-range">
                    <div class="filter-date-input">
                        <label for="filter-date-from">From</label>
                        <input type="date" id="filter-date-from" class="filter-date">
                    </div>
                    <div class="filter-date-input">
                        <label for="filter-date-to">To</label>
                        <input type="date" id="filter-date-to" class="filter-date">
                    </div>
                </div>
            </div>
        </div>
        <div class="filter-offcanvas-actions">
            <button class="mdc-button mdc-button--outlined" id="filter-clear">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Clear All</span>
            </button>
            <button class="mdc-button mdc-button--raised" id="filter-apply">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Apply Filters</span>
            </button>
        </div>
    </div>
</div>
