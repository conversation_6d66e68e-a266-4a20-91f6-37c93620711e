/* Layout and Grid Styles */

/* Main Layout Structure */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--md-background);
}

/* App Bar */
.app-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  background-color: var(--md-primary);
  color: var(--md-on-primary);
  display: flex;
  align-items: center;
  padding: 0 var(--md-spacing-md);
  box-shadow: var(--md-elevation-2);
  z-index: var(--md-z-fixed);
  transition: all var(--md-transition-normal);
}

.app-bar h1 {
  flex: 1;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 var(--md-spacing-md);
}

.app-bar .actions {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-sm);
}

.app-bar .material-icons {
  cursor: pointer;
  padding: var(--md-spacing-sm);
  border-radius: var(--md-border-radius-full);
  transition: background-color var(--md-transition-fast);
}

.app-bar .material-icons:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 64px;
  left: 0;
  width: 280px;
  height: calc(100vh - 64px);
  background-color: var(--md-surface);
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  transform: translateX(-100%);
  transition: transform var(--md-transition-normal);
  z-index: var(--md-z-fixed);
  overflow-y: auto;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all var(--md-transition-normal);
  z-index: calc(var(--md-z-fixed) - 1);
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Main Content */
.main-content {
  margin-top: 64px;
  padding: var(--md-spacing-lg);
  min-height: calc(100vh - 64px);
  transition: margin-left var(--md-transition-normal);
}

/* Content Header */
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--md-spacing-lg);
  padding-bottom: var(--md-spacing-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.page-title {
  font-size: var(--md-font-size-h2);
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--md-spacing-sm);
}

.page-title-count {
  font-size: var(--md-font-size-body2);
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
}

.content-header .actions {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-sm);
}

/* Page Sections */
.page-section {
  animation: fadeIn var(--md-transition-normal) ease-out;
}

/* Grid Systems */
.grid {
  display: grid;
  gap: var(--md-spacing-md);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Auto-fit grids */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

/* Bottom Navigation (Mobile) */
.bottom-app-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background-color: var(--md-surface);
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  display: none;
  align-items: center;
  justify-content: space-around;
  z-index: var(--md-z-fixed);
  padding: 0 var(--md-spacing-md);
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--md-spacing-xs);
  cursor: pointer;
  transition: color var(--md-transition-fast);
  color: rgba(0, 0, 0, 0.6);
  min-width: 64px;
}

.bottom-nav-item.active {
  color: var(--md-primary);
}

.bottom-nav-item .material-icons {
  font-size: 24px;
  margin-bottom: 2px;
}

.bottom-nav-label {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Container Sizes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--md-spacing-md);
}

.container-sm {
  max-width: 576px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 992px;
}

.container-xl {
  max-width: 1200px;
}

.container-fluid {
  width: 100%;
  padding: 0 var(--md-spacing-md);
}

/* Responsive Design */
@media (min-width: 1024px) {
  .sidebar {
    transform: translateX(0);
    position: relative;
    top: 0;
    height: calc(100vh - 64px);
  }
  
  .main-content {
    margin-left: 280px;
  }
  
  .sidebar-overlay {
    display: none;
  }
}

@media (max-width: 768px) {
  .app-bar {
    height: 56px;
  }
  
  .sidebar {
    top: 56px;
    height: calc(100vh - 56px);
  }
  
  .main-content {
    margin-top: 56px;
    margin-bottom: 64px;
    padding: var(--md-spacing-md);
  }
  
  .bottom-app-bar {
    display: flex;
  }
  
  .content-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--md-spacing-md);
  }
  
  .content-header .actions {
    justify-content: space-between;
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--md-spacing-sm);
  }
  
  .container,
  .container-fluid {
    padding: 0 var(--md-spacing-sm);
  }
}
