<!-- Mobile Search Overlay -->
<div class="mobile-search-overlay" id="mobile-search-overlay">
    <div class="mobile-search" id="mobile-search">
        <div class="mobile-search-header">
            <button class="mobile-search-back" id="mobile-search-back">
                <span class="material-icons">arrow_back</span>
            </button>
            <div class="mobile-search-input-container">
                <input type="text" class="mobile-search-input" id="mobile-search-input" placeholder="Search students...">
                <button class="mobile-search-clear" id="mobile-search-clear">
                    <span class="material-icons">clear</span>
                </button>
            </div>
        </div>
        <div class="mobile-search-content">
            <div class="mobile-search-suggestions" id="mobile-search-suggestions">
                <!-- Recent searches -->
                <div class="search-section">
                    <div class="search-section-title">Recent Searches</div>
                    <div class="search-suggestion" data-query="<PERSON>">
                        <span class="material-icons">history</span>
                        <span class="search-suggestion-text"><PERSON></span>
                        <button class="search-suggestion-remove">
                            <span class="material-icons">close</span>
                        </button>
                    </div>
                    <div class="search-suggestion" data-query="Grade 10">
                        <span class="material-icons">history</span>
                        <span class="search-suggestion-text">Grade 10</span>
                        <button class="search-suggestion-remove">
                            <span class="material-icons">close</span>
                        </button>
                    </div>
                </div>

                <!-- Quick filters -->
                <div class="search-section">
                    <div class="search-section-title">Quick Filters</div>
                    <div class="search-filters">
                        <div class="search-filter" data-filter="active">
                            <span class="material-icons">check_circle</span>
                            <span>Active Students</span>
                        </div>
                        <div class="search-filter" data-filter="inactive">
                            <span class="material-icons">cancel</span>
                            <span>Inactive Students</span>
                        </div>
                        <div class="search-filter" data-filter="paid">
                            <span class="material-icons">paid</span>
                            <span>Fully Paid</span>
                        </div>
                        <div class="search-filter" data-filter="unpaid">
                            <span class="material-icons">money_off</span>
                            <span>Outstanding Fees</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search results -->
            <div class="mobile-search-results" id="mobile-search-results" style="display: none;">
                <div class="search-results-header">
                    <span class="search-results-count" id="search-results-count">0 results</span>
                </div>
                <div class="search-results-list" id="search-results-list">
                    <!-- Results will be populated dynamically -->
                </div>
            </div>

            <!-- No results -->
            <div class="mobile-search-no-results" id="mobile-search-no-results" style="display: none;">
                <div class="no-results-icon">
                    <span class="material-icons">search_off</span>
                </div>
                <div class="no-results-title">No results found</div>
                <div class="no-results-message">Try adjusting your search terms or filters</div>
            </div>
        </div>
    </div>
</div>
