<!-- QR Code Scanner Modal -->
<div class="qr-scanner-overlay" id="qr-scanner-overlay">
    <div class="qr-scanner-modal" id="qr-scanner-modal">
        <div class="qr-scanner-header">
            <h2 class="qr-scanner-title">Scan QR Code</h2>
            <button class="qr-scanner-close" id="qr-scanner-close">
                <span class="material-icons">close</span>
            </button>
        </div>
        <div class="qr-scanner-content">
            <div class="qr-scanner-instructions">
                <span class="material-icons">qr_code_scanner</span>
                <p>Position the QR code within the frame to scan</p>
            </div>
            
            <!-- QR Reader Container -->
            <div id="qr-reader"></div>
            
            <!-- Scanner Controls -->
            <div class="qr-scanner-controls">
                <button class="mdc-button mdc-button--outlined" id="qr-start-scan">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons">play_arrow</span>
                    <span class="mdc-button__label">Start Scanning</span>
                </button>
                <button class="mdc-button mdc-button--outlined" id="qr-stop-scan" style="display: none;">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons">stop</span>
                    <span class="mdc-button__label">Stop Scanning</span>
                </button>
                <button class="mdc-button mdc-button--outlined" id="qr-switch-camera">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons">flip_camera_android</span>
                    <span class="mdc-button__label">Switch Camera</span>
                </button>
            </div>

            <!-- Scan Result -->
            <div class="qr-scan-result" id="qr-scan-result" style="display: none;">
                <div class="scan-result-icon">
                    <span class="material-icons">check_circle</span>
                </div>
                <div class="scan-result-title">QR Code Scanned Successfully</div>
                <div class="scan-result-data" id="qr-scan-data"></div>
                <div class="scan-result-actions">
                    <button class="mdc-button mdc-button--outlined" id="qr-scan-again">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Scan Again</span>
                    </button>
                    <button class="mdc-button mdc-button--raised" id="qr-process-result">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Process</span>
                    </button>
                </div>
            </div>

            <!-- Error State -->
            <div class="qr-scan-error" id="qr-scan-error" style="display: none;">
                <div class="scan-error-icon">
                    <span class="material-icons">error</span>
                </div>
                <div class="scan-error-title">Scanner Error</div>
                <div class="scan-error-message" id="qr-error-message"></div>
                <button class="mdc-button mdc-button--outlined" id="qr-retry-scan">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Try Again</span>
                </button>
            </div>
        </div>
    </div>
</div>
