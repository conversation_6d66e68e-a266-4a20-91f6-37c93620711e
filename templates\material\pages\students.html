{% extends 'material/base.html' %}

{% block title %}Students - {{ block.super }}{% endblock %}

{% block content %}
<div class="content-header">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span x-text="pageTitle"></span>
        <span class="page-title-count" x-show="activeNav === 'students'" x-text="`(${filteredData.length})`"></span>
    </h2>
    <div class="actions" x-show="activeNav === 'students'">
        <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
            <span class="mdc-button__label">Add Student</span>
        </button>
        <span class="material-icons hidden" title="Export to Excel">description</span>
        <span class="material-icons hidden" title="Import Data">file_upload</span>
        <span class="material-icons hidden" title="Toggle View">view_module</span>
        <span class="material-icons search-icon-mobile" title="Search" id="mobile-search-btn">search</span>
        <span class="material-icons" title="Filter" id="filter-btn">filter_list</span>
    </div>
    <div class="actions" x-show="activeNav === 'levels'">
        <button class="mdc-button mdc-button--raised add-btn">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
        </button>
        <button class="mdc-button mdc-button--outlined">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">file_download</span>
            <span class="mdc-button__label">Export</span>
        </button>
    </div>
</div>

<!-- Include page sections -->
{% include 'material/sections/dashboard.html' %}
{% include 'material/sections/levels.html' %}
{% include 'material/sections/students_list.html' %}
{% endblock %}
